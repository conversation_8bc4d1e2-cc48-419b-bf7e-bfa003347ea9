{"name": "bilal-thumbnails-portfolio", "version": "1.0.0", "type": "module", "license": "MIT", "description": "Professional YouTube thumbnail designer portfolio website", "author": "BilalThumbnails", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@tailwindcss/typography": "^0.5.15", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^11.18.2", "gsap": "^3.13.0", "input-otp": "^1.4.2", "keen-slider": "^6.8.6", "lottie-react": "^2.4.1", "lucide-react": "^0.453.0", "next-themes": "^0.4.6", "postcss": "^8.4.47", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "terser": "^5.43.1", "tw-animate-css": "^1.2.5", "typescript": "5.6.3", "vaul": "^1.1.2", "vite": "^5.4.19", "wouter": "^3.3.5", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "20.16.11", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1"}}