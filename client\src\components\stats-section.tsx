import { motion, useSpring, useTransform } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState, useEffect } from "react";

export default function StatsSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [counters, setCounters] = useState({
    thumbnails: 0,
    experience: 0,
    satisfaction: 0,
  });

  // Spring animations for smooth counting
  const thumbnailsSpring = useSpring(0, { stiffness: 100, damping: 30 });
  const experienceSpring = useSpring(0, { stiffness: 100, damping: 30 });
  const satisfactionSpring = useSpring(0, { stiffness: 100, damping: 30 });

  const stats = [
    {
      number: 500,
      suffix: "+",
      title: "Thumbnails Designed",
      description:
        "Over 500 custom thumbnails delivered for creators across gaming, vlogging, education, and lifestyle categories. Each design strategically crafted to maximize click-through rates.",
      icon: "fas fa-image",
      color: "from-orange-500 to-red-500",
    },
    {
      number: 8,
      suffix: "+",
      title: "Years of Experience",
      description:
        "Eight years of professional design experience, specializing in YouTube thumbnails and social media graphics that drive engagement and growth.",
      icon: "fas fa-calendar-alt",
      color: "from-blue-500 to-indigo-500",
    },
    {
      number: 98,
      suffix: "%",
      title: "Client Satisfaction",
      description:
        "Maintaining a 98% satisfaction rate by consistently delivering high-quality, results-driven designs that exceed client expectations and boost performance.",
      icon: "fas fa-heart",
      color: "from-green-500 to-teal-500",
    },
  ];

  // Animated counter effect
  useEffect(() => {
    if (isInView) {
      const duration = 2000; // 2 seconds
      const steps = 60;
      const stepTime = duration / steps;

      stats.forEach((stat, index) => {
        let currentStep = 0;
        const increment = stat.number / steps;

        const timer = setInterval(() => {
          currentStep++;
          const currentValue = Math.min(
            Math.floor(increment * currentStep),
            stat.number
          );

          setCounters((prev) => ({
            ...prev,
            [index === 0
              ? "thumbnails"
              : index === 1
              ? "experience"
              : "satisfaction"]: currentValue,
          }));

          if (currentStep >= steps) {
            clearInterval(timer);
          }
        }, stepTime);
      });
    }
  }, [isInView]);

  return (
    <section
      id='stats'
      className='py-16 md:py-24 lg:py-32 px-6 lg:px-12 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden'
      ref={ref}
    >
      {/* Background Elements */}
      <div className='absolute inset-0 z-0'>
        <motion.div
          className='absolute top-1/3 left-1/6 w-32 h-32 bg-gradient-to-br from-[#FF4500]/10 to-[#FF6B35]/5 rounded-full blur-2xl'
          animate={{
            scale: [1, 1.3, 1],
            x: [0, 30, 0],
            y: [0, -20, 0],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className='absolute bottom-1/3 right-1/6 w-24 h-24 bg-gradient-to-tl from-[#FFA500]/10 to-[#FF4500]/5 rounded-full blur-2xl'
          animate={{
            scale: [1.2, 1, 1.2],
            x: [0, -25, 0],
            y: [0, 15, 0],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: -3,
          }}
        />
      </div>

      <div className='max-w-7xl mx-auto relative z-10'>
        {/* Header */}
        <motion.div
          className='text-center space-y-6 mb-16'
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.p
            className='text-sm font-semibold text-[#FF4500] uppercase tracking-wide'
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            OUR ACHIEVEMENTS
          </motion.p>

          <motion.h2
            className='text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight tracking-wide'
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            PROVEN{" "}
            <span className='bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent'>
              RESULTS
            </span>
          </motion.h2>
        </motion.div>

        {/* Stats Grid */}
        <div className='grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-10'>
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              className='group relative bg-white rounded-2xl p-8 shadow-lg border border-gray-100 transition-all duration-500 overflow-hidden text-center hover:bg-gradient-to-br hover:from-[#FF4500]/5 hover:to-[#FF6B35]/5'
              initial={{ opacity: 0, y: 50 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              whileHover={{
                y: -10,
                scale: 1.03,
                boxShadow: "0 25px 50px rgba(255, 69, 0, 0.2)",
              }}
            >
              {/* Background gradient on hover */}
              <div className='absolute inset-0 bg-gradient-to-br from-white to-gray-50 opacity-0 group-hover:opacity-100 transition-opacity duration-500' />

              <div className='relative z-10 space-y-6'>
                {/* Icon */}
                <motion.div
                  className={`w-16 h-16 mx-auto rounded-2xl bg-gradient-to-r ${stat.color} flex items-center justify-center shadow-lg`}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <i className={`${stat.icon} text-white text-2xl`} />
                </motion.div>

                {/* Number with Creative Animation */}
                <div className='space-y-2 relative'>
                  {/* Animated Background Circle */}
                  <motion.div
                    className='absolute inset-0 rounded-full bg-gradient-to-r from-[#FF4500]/10 to-[#FF6B35]/10'
                    initial={{ scale: 0, rotate: 0 }}
                    animate={
                      isInView
                        ? { scale: 1.2, rotate: 360 }
                        : { scale: 0, rotate: 0 }
                    }
                    transition={{
                      duration: 2,
                      delay: index * 0.3,
                      type: "spring",
                      stiffness: 100,
                    }}
                  />

                  <motion.h3
                    className='text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent relative z-10'
                    initial={{ scale: 0, rotateY: -180 }}
                    animate={
                      isInView
                        ? { scale: 1, rotateY: 0 }
                        : { scale: 0, rotateY: -180 }
                    }
                    transition={{
                      duration: 1.2,
                      delay: index * 0.2 + 0.5,
                      type: "spring",
                      stiffness: 200,
                      damping: 20,
                    }}
                    whileHover={{
                      scale: 1.1,
                      textShadow: "0 0 20px rgba(255, 69, 0, 0.5)",
                    }}
                  >
                    {index === 0
                      ? counters.thumbnails
                      : index === 1
                      ? counters.experience
                      : counters.satisfaction}
                    {stat.suffix}
                  </motion.h3>

                  <motion.h4
                    className='text-xl md:text-2xl font-bold text-gray-900 group-hover:text-[#FF4500] transition-colors duration-300 relative z-10'
                    initial={{ opacity: 0, y: 20 }}
                    animate={
                      isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }
                    }
                    transition={{ duration: 0.8, delay: index * 0.2 + 0.8 }}
                  >
                    {stat.title}
                  </motion.h4>
                </div>

                {/* Description */}
                <p className='text-gray-600 leading-relaxed text-base'>
                  {stat.description}
                </p>
              </div>

              {/* Decorative elements */}
              <div className='absolute top-4 right-4 w-20 h-20 bg-gradient-to-br from-[#FF4500]/5 to-[#FF6B35]/5 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500' />
              <div className='absolute bottom-4 left-4 w-16 h-16 bg-gradient-to-br from-[#FFA500]/5 to-[#FF4500]/5 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500' />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
