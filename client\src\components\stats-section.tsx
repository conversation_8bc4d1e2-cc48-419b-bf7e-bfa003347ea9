import { useRef, useState, useEffect } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

export default function StatsSection() {
  const sectionRef = useRef(null);
  const [counters, setCounters] = useState([0, 0, 0]);

  const stats = [
    {
      number: 500,
      suffix: "+",
      title: "Thumbnails Designed",
      description: "Custom thumbnails delivered for creators across all niches",
      icon: "fas fa-image",
      color: "from-orange-500 to-red-500",
    },
    {
      number: 8,
      suffix: "+",
      title: "Years Experience",
      description: "Professional design experience in digital marketing",
      icon: "fas fa-calendar-alt",
      color: "from-blue-500 to-indigo-500",
    },
    {
      number: 98,
      suffix: "%",
      title: "Client Satisfaction",
      description: "Satisfaction rate with unlimited revisions guarantee",
      icon: "fas fa-heart",
      color: "from-green-500 to-teal-500",
    },
  ];

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Counter animations
      stats.forEach((stat, index) => {
        gsap.fromTo(
          {},
          {
            duration: 2,
            ease: "power2.out",
            scrollTrigger: {
              trigger: sectionRef.current,
              start: "top 80%",
              onEnter: () => {
                gsap.to(
                  {},
                  {
                    duration: 2,
                    ease: "power2.out",
                    onUpdate: function () {
                      const progress = this.progress();
                      const currentValue = Math.floor(stat.number * progress);
                      setCounters((prev) => {
                        const newCounters = [...prev];
                        newCounters[index] = currentValue;
                        return newCounters;
                      });
                    },
                  }
                );
              },
            },
          }
        );
      });

      // Card animations
      gsap.fromTo(
        ".stat-card",
        { y: 100, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 1,
          stagger: 0.2,
          ease: "power3.out",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 80%",
            toggleActions: "play none none reverse",
          },
        }
      );
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  return (
    <section
      id='stats'
      className='py-16 md:py-24 lg:py-32 px-6 lg:px-12 bg-white'
      ref={sectionRef}
    >
      <div className='max-w-7xl mx-auto'>
        {/* Header */}
        <div className='text-center space-y-4 mb-16'>
          <p className='text-sm font-semibold text-[#FF4500] uppercase tracking-wide'>
            OUR ACHIEVEMENTS
          </p>
          <h2 className='text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight tracking-wide'>
            PROVEN{" "}
            <span className='bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent'>
              RESULTS
            </span>
          </h2>
        </div>

        {/* Stats Grid */}
        <div className='grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto'>
          {stats.map((stat, index) => (
            <div
              key={index}
              className='stat-card group relative bg-white rounded-2xl p-8 shadow-lg border border-gray-100 transition-all duration-500 text-center hover:bg-gradient-to-br hover:from-[#FF4500]/5 hover:to-[#FF6B35]/5 hover:shadow-2xl hover:-translate-y-2'
            >
              {/* Icon */}
              <div
                className={`w-16 h-16 mx-auto mb-6 rounded-2xl bg-gradient-to-r ${stat.color} flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300`}
              >
                <i className={`${stat.icon} text-white text-2xl`} />
              </div>

              {/* Number */}
              <div className='mb-4'>
                <h3 className='text-5xl font-bold bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300'>
                  {counters[index]}
                  {stat.suffix}
                </h3>
              </div>

              {/* Title */}
              <h4 className='text-xl font-bold text-gray-900 mb-3 group-hover:text-[#FF4500] transition-colors duration-300'>
                {stat.title}
              </h4>

              {/* Description */}
              <p className='text-gray-600 text-sm leading-relaxed'>
                {stat.description}
              </p>

              {/* Hover Effect */}
              <div className='absolute inset-0 bg-gradient-to-br from-[#FF4500]/5 to-[#FF6B35]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl' />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
