import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState, useEffect } from "react";

export default function TestimonialsSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [satisfactionRate, setSatisfactionRate] = useState(0);

  const testimonials = [
    {
      text: "Biviso transformed our channel's visual identity completely. Our click-through rates increased by 45% within the first month. Their team understands what makes viewers click!",
      name: "ALEX MARTINEZ",
      role: "Gaming YouTuber • 500K Subscribers",
      avatar: "/testimonial-avatar-1.jpg",
      rating: 5,
      metric: "+45% CTR",
      color: "from-blue-500 to-indigo-500",
    },
    {
      text: "Working with Biviso was a game-changer. They don't just create beautiful designs – they create strategic visuals that convert. Our subscriber growth doubled!",
      name: "SARAH JOHNSON",
      role: "Lifestyle Influencer • 320K Subscribers",
      avatar: "/testimonial-avatar-2.jpg",
      rating: 5,
      metric: "2x Growth",
      color: "from-pink-500 to-rose-500",
    },
    {
      text: "Professional, fast, and incredibly creative. Biviso's thumbnails helped us reach 1M views faster than we ever imagined. Highly recommend their services!",
      name: "DAVID CHEN",
      role: "Tech Reviewer • 750K Subscribers",
      avatar: "/testimonial-avatar-3.jpg",
      rating: 5,
      metric: "1M+ Views",
      color: "from-green-500 to-emerald-500",
    },
    {
      text: "The attention to detail and understanding of our brand was exceptional. Biviso created thumbnails that perfectly captured our content's essence while boosting performance.",
      name: "EMMA WILSON",
      role: "Educational Content Creator • 180K Subscribers",
      avatar: "/testimonial-avatar-4.jpg",
      rating: 5,
      metric: "+60% Engagement",
      color: "from-purple-500 to-violet-500",
    },
  ];

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [testimonials.length]);

  // Animate satisfaction rate
  useEffect(() => {
    if (isInView) {
      const timer = setTimeout(() => {
        let current = 0;
        const target = 98.6;
        const increment = target / 60;

        const counter = setInterval(() => {
          current += increment;
          if (current >= target) {
            setSatisfactionRate(target);
            clearInterval(counter);
          } else {
            setSatisfactionRate(current);
          }
        }, 30);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [isInView]);

  return (
    <section
      id='testimonials'
      className='py-16 md:py-24 lg:py-32 px-6 lg:px-12 bg-white relative overflow-hidden'
      ref={ref}
    >
      {/* Background Elements */}
      <div className='absolute inset-0 z-0'>
        <motion.div
          className='absolute top-1/4 right-1/4 w-64 h-64 bg-gradient-to-br from-[#FF4500]/5 to-[#FF6B35]/10 rounded-full blur-3xl'
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className='absolute bottom-1/4 left-1/4 w-48 h-48 bg-gradient-to-tl from-[#FFA500]/5 to-[#FF4500]/10 rounded-full blur-3xl'
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut",
            delay: -5,
          }}
        />
      </div>

      <div className='max-w-7xl mx-auto relative z-10'>
        {/* Header */}
        <motion.div
          className='text-center space-y-6 mb-16'
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.p
            className='text-sm font-semibold text-[#FF4500] uppercase tracking-wide'
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            CLIENT TESTIMONIALS
          </motion.p>

          <motion.h2
            className='text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight tracking-wide'
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            WHAT OUR{" "}
            <span className='bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent'>
              CLIENTS SAY
            </span>
          </motion.h2>
        </motion.div>

        {/* Testimonials Grid */}
        <div className='grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto'>
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              className='group relative bg-white rounded-3xl p-8 shadow-xl border border-gray-100 transition-all duration-500 hover:bg-gradient-to-br hover:from-[#FF4500]/5 hover:to-[#FF6B35]/5 overflow-hidden'
              initial={{ opacity: 0, y: 50 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              whileHover={{
                y: -12,
                scale: 1.03,
                boxShadow: "0 30px 60px rgba(255, 69, 0, 0.2)",
              }}
            >
              {/* Background Pattern */}
              <div className='absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-[#FF4500]/5 to-[#FF6B35]/10 rounded-full blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500' />

              {/* Metric Badge */}
              <motion.div
                className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-bold text-white bg-gradient-to-r ${testimonial.color} mb-6 shadow-lg`}
                whileHover={{ scale: 1.1, rotate: 2 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <i className='fas fa-chart-line mr-2' />
                {testimonial.metric}
              </motion.div>

              {/* Quote Section */}
              <div className='relative mb-6'>
                <motion.div
                  className='absolute -top-2 -left-2 text-6xl text-[#FF4500]/20 font-serif'
                  initial={{ scale: 0, rotate: -45 }}
                  animate={
                    isInView
                      ? { scale: 1, rotate: 0 }
                      : { scale: 0, rotate: -45 }
                  }
                  transition={{ duration: 0.8, delay: index * 0.2 + 0.3 }}
                >
                  "
                </motion.div>

                {/* Stars */}
                <div className='flex text-[#FF4500] text-lg mb-4 relative z-10'>
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <motion.i
                      key={i}
                      className='fas fa-star'
                      initial={{ opacity: 0, scale: 0, rotate: -180 }}
                      animate={
                        isInView
                          ? { opacity: 1, scale: 1, rotate: 0 }
                          : { opacity: 0, scale: 0, rotate: -180 }
                      }
                      transition={{
                        duration: 0.5,
                        delay: index * 0.2 + i * 0.1,
                        type: "spring",
                        stiffness: 200,
                      }}
                      whileHover={{ scale: 1.3, rotate: 360 }}
                    />
                  ))}
                </div>

                {/* Quote */}
                <blockquote className='text-gray-700 text-lg leading-relaxed font-medium relative z-10'>
                  {testimonial.text}
                </blockquote>
              </div>

              {/* Author Section */}
              <motion.div
                className='flex items-center space-x-4 relative z-10'
                initial={{ opacity: 0, x: -20 }}
                animate={
                  isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }
                }
                transition={{ duration: 0.6, delay: index * 0.2 + 0.5 }}
              >
                <motion.div
                  className={`w-14 h-14 rounded-full bg-gradient-to-r ${testimonial.color} flex items-center justify-center shadow-lg`}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <span className='text-white font-bold text-lg'>
                    {testimonial.name.charAt(0)}
                  </span>
                </motion.div>
                <div>
                  <h4 className='font-bold text-gray-900 text-lg group-hover:text-[#FF4500] transition-colors duration-300'>
                    {testimonial.name}
                  </h4>
                  <p className='text-gray-600 text-sm font-medium'>
                    {testimonial.role}
                  </p>
                </div>
              </motion.div>

              {/* Interactive Hover Elements */}
              <motion.div
                className='absolute bottom-4 right-4 w-8 h-8 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300'
                whileHover={{ scale: 1.5, rotate: 180 }}
              />

              <motion.div
                className='absolute top-4 left-4 w-6 h-6 border-2 border-[#FF6B35]/30 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300'
                animate={{ rotate: 360 }}
                transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              />
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          className='text-center mt-16'
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <motion.a
            href='#contact'
            className='inline-flex items-center px-8 py-4 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white font-semibold text-sm tracking-wide rounded-full shadow-xl hover:shadow-2xl transition-all duration-300'
            whileHover={{
              scale: 1.05,
              boxShadow: "0 20px 40px rgba(255, 69, 0, 0.3)",
            }}
            whileTap={{ scale: 0.95 }}
          >
            <span className='mr-3'>JOIN OUR SUCCESS STORIES</span>
            <motion.i
              className='fas fa-arrow-right text-sm'
              whileHover={{ x: 5 }}
              transition={{ duration: 0.2 }}
            />
          </motion.a>
        </motion.div>
      </div>
    </section>
  );
}
