import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState, useEffect } from "react";

export default function TestimonialsSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [satisfactionRate, setSatisfactionRate] = useState(0);

  const testimonials = [
    {
      text: "Biviso transformed our channel's visual identity completely. Our click-through rates increased by 45% within the first month. Their team understands what makes viewers click!",
      name: "ALEX MARTINEZ",
      role: "Gaming YouTuber • 500K Subscribers",
      avatar: "/testimonial-avatar-1.jpg",
      rating: 5,
      metric: "+45% CTR",
      color: "from-blue-500 to-indigo-500",
    },
    {
      text: "Working with Biviso was a game-changer. They don't just create beautiful designs – they create strategic visuals that convert. Our subscriber growth doubled!",
      name: "SARAH JOHNSON",
      role: "Lifestyle Influencer • 320K Subscribers",
      avatar: "/testimonial-avatar-2.jpg",
      rating: 5,
      metric: "2x Growth",
      color: "from-pink-500 to-rose-500",
    },
    {
      text: "Professional, fast, and incredibly creative. Biviso's thumbnails helped us reach 1M views faster than we ever imagined. Highly recommend their services!",
      name: "DAVID CHEN",
      role: "Tech Reviewer • 750K Subscribers",
      avatar: "/testimonial-avatar-3.jpg",
      rating: 5,
      metric: "1M+ Views",
      color: "from-green-500 to-emerald-500",
    },
    {
      text: "The attention to detail and understanding of our brand was exceptional. Biviso created thumbnails that perfectly captured our content's essence while boosting performance.",
      name: "EMMA WILSON",
      role: "Educational Content Creator • 180K Subscribers",
      avatar: "/testimonial-avatar-4.jpg",
      rating: 5,
      metric: "+60% Engagement",
      color: "from-purple-500 to-violet-500",
    },
  ];

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [testimonials.length]);

  // Animate satisfaction rate
  useEffect(() => {
    if (isInView) {
      const timer = setTimeout(() => {
        let current = 0;
        const target = 98.6;
        const increment = target / 60;

        const counter = setInterval(() => {
          current += increment;
          if (current >= target) {
            setSatisfactionRate(target);
            clearInterval(counter);
          } else {
            setSatisfactionRate(current);
          }
        }, 30);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [isInView]);

  return (
    <section
      id='testimonials'
      className='py-16 md:py-24 lg:py-32 px-6 lg:px-12 bg-white relative overflow-hidden'
      ref={ref}
    >
      {/* Background Elements */}
      <div className='absolute inset-0 z-0'>
        <motion.div
          className='absolute top-1/4 right-1/4 w-64 h-64 bg-gradient-to-br from-[#FF4500]/5 to-[#FF6B35]/10 rounded-full blur-3xl'
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className='absolute bottom-1/4 left-1/4 w-48 h-48 bg-gradient-to-tl from-[#FFA500]/5 to-[#FF4500]/10 rounded-full blur-3xl'
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut",
            delay: -5,
          }}
        />
      </div>

      <div className='max-w-7xl mx-auto relative z-10'>
        {/* Header */}
        <motion.div
          className='text-center space-y-6 mb-16'
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.p
            className='text-sm font-semibold text-[#FF4500] uppercase tracking-wide'
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            CLIENT TESTIMONIALS
          </motion.p>

          <motion.h2
            className='text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight tracking-wide'
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            WHAT OUR{" "}
            <span className='bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent'>
              CLIENTS SAY
            </span>
          </motion.h2>
        </motion.div>

        {/* Testimonials Grid */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              className='group relative bg-white rounded-2xl p-6 shadow-lg border border-gray-100 transition-all duration-500 hover:bg-gradient-to-br hover:from-[#FF4500]/5 hover:to-[#FF6B35]/5'
              initial={{ opacity: 0, y: 50 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{
                y: -8,
                scale: 1.02,
                boxShadow: "0 25px 50px rgba(255, 69, 0, 0.15)",
              }}
            >
              {/* Metric Badge */}
              <motion.div
                className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold text-white bg-gradient-to-r ${testimonial.color} mb-4`}
                whileHover={{ scale: 1.05 }}
              >
                {testimonial.metric}
              </motion.div>

              {/* Stars */}
              <div className='flex text-[#FF4500] text-sm mb-4'>
                {[...Array(testimonial.rating)].map((_, i) => (
                  <motion.i
                    key={i}
                    className='fas fa-star'
                    initial={{ opacity: 0, scale: 0 }}
                    animate={
                      isInView
                        ? { opacity: 1, scale: 1 }
                        : { opacity: 0, scale: 0 }
                    }
                    transition={{ duration: 0.3, delay: index * 0.1 + i * 0.1 }}
                  />
                ))}
              </div>

              {/* Quote */}
              <blockquote className='text-gray-700 text-sm leading-relaxed mb-6'>
                "{testimonial.text}"
              </blockquote>

              {/* Author */}
              <div className='flex items-center space-x-3'>
                <div
                  className={`w-10 h-10 rounded-full bg-gradient-to-r ${testimonial.color} flex items-center justify-center`}
                >
                  <span className='text-white font-bold text-sm'>
                    {testimonial.name.charAt(0)}
                  </span>
                </div>
                <div>
                  <h4 className='font-semibold text-gray-900 text-sm group-hover:text-[#FF4500] transition-colors duration-300'>
                    {testimonial.name}
                  </h4>
                  <p className='text-gray-600 text-xs'>{testimonial.role}</p>
                </div>
              </div>

              {/* Hover Effect */}
              <motion.div
                className='absolute inset-0 bg-gradient-to-br from-[#FF4500]/10 to-[#FF6B35]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl'
                whileHover={{ scale: 1.02 }}
              />
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          className='text-center mt-16'
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <motion.a
            href='#contact'
            className='inline-flex items-center px-8 py-4 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white font-semibold text-sm tracking-wide rounded-full shadow-xl hover:shadow-2xl transition-all duration-300'
            whileHover={{
              scale: 1.05,
              boxShadow: "0 20px 40px rgba(255, 69, 0, 0.3)",
            }}
            whileTap={{ scale: 0.95 }}
          >
            <span className='mr-3'>JOIN OUR SUCCESS STORIES</span>
            <motion.i
              className='fas fa-arrow-right text-sm'
              whileHover={{ x: 5 }}
              transition={{ duration: 0.2 }}
            />
          </motion.a>
        </motion.div>
      </div>
    </section>
  );
}
