import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

export default function Navigation() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState("hero");

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 30);

      const sections = [
        "hero",
        "services",
        "portfolio",
        "before-after",
        "testimonials",
        "about",
        "problem-solution",
        "offers",
        "contact",
      ];
      const currentSection = sections.find((section) => {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          return rect.top <= 100 && rect.bottom >= 100;
        }
        return false;
      });
      if (currentSection) setActiveSection(currentSection);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const mainNavItems = [
    { href: "#hero", label: "Home", id: "hero", icon: "fas fa-home" },
    {
      href: "#services",
      label: "Services",
      id: "services",
      icon: "fas fa-cogs",
    },
    {
      href: "#portfolio",
      label: "Portfolio",
      id: "portfolio",
      icon: "fas fa-folder-open",
    },
    {
      href: "#before-after",
      label: "Results",
      id: "before-after",
      icon: "fas fa-chart-line",
    },
  ];

  const moreNavItems = [
    {
      href: "#features",
      label: "Features",
      id: "features",
      icon: "fas fa-star",
    },
    { href: "#about", label: "About", id: "about", icon: "fas fa-users" },
    {
      href: "#stats",
      label: "Stats",
      id: "stats",
      icon: "fas fa-trophy",
    },
    {
      href: "#testimonials",
      label: "Reviews",
      id: "testimonials",
      icon: "fas fa-quote-left",
    },
    {
      href: "#offers",
      label: "Pricing",
      id: "offers",
      icon: "fas fa-dollar-sign",
    },
    {
      href: "#contact",
      label: "Contact",
      id: "contact",
      icon: "fas fa-envelope",
    },
  ];

  return (
    <motion.nav
      className='fixed top-0 left-0 right-0 z-50 transition-all duration-300'
      style={{
        background: isScrolled
          ? "rgba(255, 255, 255, 0.95)"
          : "rgba(255, 255, 255, 0.9)",
        backdropFilter: "blur(20px)",
      }}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-12'>
        <div className='flex items-center justify-between h-16 sm:h-18'>
          {/* Logo */}
          <motion.div
            className='flex items-center flex-shrink-0'
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <img
              src='/Bisiso logo.png'
              alt='Biviso Logo'
              className='w-14 h-14 sm:w-16 sm:h-16 object-contain flex-shrink-0'
            />
          </motion.div>

          {/* Center Navigation Icons */}
          <div className='hidden lg:flex items-center justify-center flex-1'>
            <div className='flex items-center space-x-1'>
              {[...mainNavItems, ...moreNavItems].map((item, index) => (
                <motion.div
                  key={item.href}
                  className='relative group'
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <motion.a
                    href={item.href}
                    className={`relative flex items-center justify-center w-12 h-12 rounded-full transition-all duration-300 ${
                      activeSection === item.id
                        ? "text-white"
                        : "text-gray-600 hover:text-[#FF4500]"
                    }`}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setActiveSection(item.id)}
                    title={item.label}
                  >
                    {activeSection === item.id && (
                      <motion.div
                        className='absolute inset-0 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-full shadow-lg'
                        layoutId='activeTab'
                        transition={{
                          type: "spring",
                          stiffness: 500,
                          damping: 30,
                        }}
                      />
                    )}
                    <i className={`${item.icon} text-base relative z-10`} />
                  </motion.a>

                  {/* Tooltip */}
                  <div className='absolute -bottom-12 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50 shadow-lg'>
                    {item.label}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Contact Button */}
          <motion.a
            href='#contact'
            className='hidden lg:flex items-center px-6 py-3 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white text-base font-medium rounded-full hover:shadow-lg transition-all duration-300'
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Contact Now
          </motion.a>

          {/* Mobile Menu Button */}
          <motion.button
            className='lg:hidden relative w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white flex items-center justify-center shadow-lg flex-shrink-0'
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <motion.div
              animate={isMobileMenuOpen ? { rotate: 180 } : { rotate: 0 }}
              transition={{ duration: 0.3 }}
            >
              <i
                className={`fas ${
                  isMobileMenuOpen ? "fa-times" : "fa-bars"
                } text-base sm:text-lg`}
              />
            </motion.div>
          </motion.button>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            className='lg:hidden absolute top-full inset-x-4 mt-4 backdrop-blur-2xl bg-white/95 rounded-3xl border border-white/20 shadow-2xl overflow-hidden'
            initial={{ opacity: 0, y: -20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.95 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            <div className='p-6'>
              <div className='flex flex-col space-y-2'>
                {[...mainNavItems, ...moreNavItems].map((item, index) => (
                  <motion.a
                    key={item.href}
                    href={item.href}
                    className='relative flex items-center space-x-4 py-4 px-6 rounded-2xl font-semibold text-gray-700 hover:text-white transition-all duration-300 group'
                    onClick={() => {
                      setIsMobileMenuOpen(false);
                      setActiveSection(item.id);
                    }}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    whileHover={{ x: 10 }}
                  >
                    <motion.div
                      className='absolute inset-0 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300'
                      whileHover={{ scale: 1.02 }}
                    />
                    <i className={`${item.icon} text-lg relative z-10`} />
                    <span className='relative z-10'>{item.label}</span>
                  </motion.a>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.nav>
  );
}
