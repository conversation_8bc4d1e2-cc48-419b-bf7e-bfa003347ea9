import { useEffect, lazy, Suspense } from "react";
import Navigation from "@/components/navigation";
import HeroSection from "@/components/hero-section";
import Loading from "@/components/loading";

// Lazy load components that are below the fold
const AboutSection = lazy(() => import("@/components/about-section"));
const OffersSection = lazy(() => import("@/components/offers-section"));
const BeforeAfterSection = lazy(
  () => import("@/components/before-after-section")
);
const ServicesSection = lazy(() => import("@/components/services-section"));
const PortfolioSection = lazy(() => import("@/components/portfolio-section"));
const FeaturesSection = lazy(() => import("@/components/features-section"));
const TestimonialsSection = lazy(
  () => import("@/components/testimonials-section")
);
const StatsSection = lazy(() => import("@/components/stats-section"));
const ProblemSolutionSection = lazy(
  () => import("@/components/problem-solution-section")
);
const ContactSection = lazy(() => import("@/components/contact-section"));
const Footer = lazy(() => import("@/components/footer"));

export default function Home() {
  useEffect(() => {
    // Smooth scrolling for navigation links
    const handleLinkClick = (e: Event) => {
      const target = e.target as HTMLAnchorElement;
      if (target.getAttribute("href")?.startsWith("#")) {
        e.preventDefault();
        const element = document.querySelector(target.getAttribute("href")!);
        if (element) {
          element.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }
    };

    document.addEventListener("click", handleLinkClick);
    return () => document.removeEventListener("click", handleLinkClick);
  }, []);

  return (
    <div className='min-h-screen bg-white text-gray-900'>
      <Navigation />
      <HeroSection />
      <Suspense fallback={<Loading message='Loading services...' />}>
        <ServicesSection />
      </Suspense>
      <Suspense fallback={<Loading message='Loading portfolio...' />}>
        <PortfolioSection />
      </Suspense>
      <Suspense fallback={<Loading message='Loading features...' />}>
        <FeaturesSection />
      </Suspense>
      <Suspense fallback={<Loading message='Loading about...' />}>
        <AboutSection />
      </Suspense>
      <Suspense fallback={<Loading message='Loading before/after...' />}>
        <BeforeAfterSection />
      </Suspense>
      <Suspense fallback={<Loading message='Loading stats...' />}>
        <StatsSection />
      </Suspense>
      <Suspense fallback={<Loading message='Loading testimonials...' />}>
        <TestimonialsSection />
      </Suspense>
      <Suspense fallback={<Loading message='Loading solutions...' />}>
        <ProblemSolutionSection />
      </Suspense>
      <Suspense fallback={<Loading message='Loading offers...' />}>
        <OffersSection />
      </Suspense>
      <Suspense fallback={<Loading message='Loading contact...' />}>
        <ContactSection />
      </Suspense>
      <Suspense fallback={<Loading message='Loading footer...' />}>
        <Footer />
      </Suspense>
    </div>
  );
}
