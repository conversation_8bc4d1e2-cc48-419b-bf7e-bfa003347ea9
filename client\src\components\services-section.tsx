import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

export default function ServicesSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const services = [
    {
      icon: "fas fa-play",
      title: "YouTube Thumbnails",
      description:
        "Scroll-stopping designs crafted to boost your CTR. We create high-performing YouTube thumbnails that grab attention, spark curiosity, and drive more views. Designed with your niche, audience, and growth goals in mind.",
      color: "from-orange-500 to-red-500",
    },
    {
      icon: "fas fa-share-alt",
      title: "Social Media Design",
      description:
        "Bold, branded visuals that get noticed. From Instagram posts to Twitter banners, we design eye-catching content that aligns with your brand and engages your followers. Perfect for creators, influencers, and brands.",
      color: "from-blue-500 to-indigo-500",
    },
    {
      icon: "fas fa-lightbulb",
      title: "Design Consultation",
      description:
        "Expert guidance to elevate your visual presence. Not sure what works best for your channel or brand? We offer personalized design consultations to help refine your aesthetic, improve engagement, and build a consistent visual strategy.",
      color: "from-purple-500 to-pink-500",
    },
  ];

  return (
    <section
      id='services'
      className='py-16 md:py-24 lg:py-32 px-6 lg:px-12 bg-white'
      ref={ref}
    >
      <div className='max-w-7xl mx-auto'>
        {/* Header */}
        <motion.div
          className='text-center space-y-6 mb-16'
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <h2 className='text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight tracking-wide'>
            <span className='bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent'>
              WHAT WE DELIVER
            </span>
          </h2>
        </motion.div>

        {/* Services Grid - Left Aligned Layout */}
        <div className='grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto'>
          {services.map((service, index) => (
            <motion.div
              key={index}
              className='group relative bg-white rounded-2xl p-8 shadow-lg border border-gray-100 transition-all duration-500 text-left hover:bg-gradient-to-br hover:from-[#FF4500]/5 hover:to-[#FF6B35]/5'
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{
                y: -8,
                boxShadow: "0 25px 50px rgba(255, 69, 0, 0.15)",
              }}
            >
              {/* Icon */}
              <motion.div
                className={`w-14 h-14 mb-6 rounded-xl bg-gradient-to-r ${service.color} flex items-center justify-center shadow-lg`}
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
              >
                <i className={`${service.icon} text-white text-xl`} />
              </motion.div>

              {/* Content */}
              <h3 className='text-xl font-bold text-gray-900 mb-4 group-hover:text-[#FF4500] transition-colors duration-300'>
                {service.title}
              </h3>
              <p className='text-gray-600 leading-relaxed text-sm line-height-loose'>
                {service.description}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
