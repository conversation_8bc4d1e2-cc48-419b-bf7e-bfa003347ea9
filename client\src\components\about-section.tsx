import { useRef, useEffect } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

export default function AboutSection() {
  const sectionRef = useRef(null);
  const contentRef = useRef(null);
  const imageRef = useRef(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Animate content on scroll
      gsap.fromTo(
        contentRef.current,
        { x: -100, opacity: 0 },
        {
          x: 0,
          opacity: 1,
          duration: 1.2,
          ease: "power3.out",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        }
      );

      gsap.fromTo(
        imageRef.current,
        { x: 100, opacity: 0 },
        {
          x: 0,
          opacity: 1,
          duration: 1.2,
          ease: "power3.out",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        }
      );
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  return (
    <section
      id='about'
      className='py-16 md:py-24 lg:py-32 px-6 lg:px-12 bg-white'
      ref={sectionRef}
    >
      <div className='max-w-7xl mx-auto'>
        {/* Header */}
        <div className='text-center space-y-4 mb-16'>
          <p className='text-sm font-semibold text-[#FF4500] uppercase tracking-wide'>
            ABOUT BIVISO
          </p>
          <h2 className='text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight tracking-wide'>
            MEET THE{" "}
            <span className='bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent'>
              CREATIVE AGENCY
            </span>
          </h2>
        </div>

        {/* Main Content */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-16 items-center'>
          {/* Left Content */}
          <div ref={contentRef} className='space-y-8 opacity-0'>
            <div className='space-y-6'>
              <h3 className='text-2xl font-bold text-gray-900'>
                Transforming Visions into Visual Success
              </h3>

              <p className='text-lg text-gray-600 leading-relaxed'>
                We are <strong className='text-[#FF4500]'>Biviso</strong>, a
                creative design agency that specializes in transforming content
                creators' visions into scroll-stopping visuals that drive real
                results.
              </p>

              <p className='text-lg text-gray-600 leading-relaxed'>
                Our team combines creative expertise with data-driven insights
                to create thumbnails, social media designs, and visual branding
                that not only look incredible but perform exceptionally in
                today's competitive digital landscape.
              </p>

              <div className='grid grid-cols-2 gap-6 pt-6'>
                <div className='text-center'>
                  <div className='text-3xl font-bold text-[#FF4500] mb-2'>
                    500+
                  </div>
                  <div className='text-sm text-gray-600'>
                    Projects Completed
                  </div>
                </div>
                <div className='text-center'>
                  <div className='text-3xl font-bold text-[#FF4500] mb-2'>
                    98%
                  </div>
                  <div className='text-sm text-gray-600'>
                    Client Satisfaction
                  </div>
                </div>
              </div>
            </div>

            <div className='flex flex-col sm:flex-row gap-4'>
              <a
                href='#contact'
                className='px-6 py-3 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white font-semibold text-sm rounded-full hover:shadow-lg transition-all duration-300'
              >
                START YOUR PROJECT
              </a>
              <a
                href='#portfolio'
                className='px-6 py-3 border-2 border-[#FF4500] text-[#FF4500] font-semibold text-sm rounded-full hover:bg-[#FF4500] hover:text-white transition-all duration-300'
              >
                VIEW OUR WORK
              </a>
            </div>
          </div>

          {/* Right Content - Visual */}
          <div ref={imageRef} className='opacity-0'>
            <div className='relative'>
              <div className='bg-gradient-to-br from-[#FF4500]/10 to-[#FF6B35]/10 rounded-3xl p-8 h-96 flex items-center justify-center'>
                <div className='text-center space-y-6'>
                  <div className='w-24 h-24 mx-auto bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-2xl flex items-center justify-center'>
                    <i className='fas fa-palette text-white text-3xl' />
                  </div>
                  <h4 className='text-xl font-bold text-gray-900'>
                    Creative Excellence
                  </h4>
                  <p className='text-gray-600 max-w-sm'>
                    Every design we create is crafted with precision,
                    creativity, and strategic thinking to ensure maximum impact.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
