import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState } from "react";

export default function AboutSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [activeTab, setActiveTab] = useState(0);

  const tabs = [
    {
      title: "Our Mission",
      content:
        "We transform creative visions into scroll-stopping visuals that drive real results. Our mission is to help content creators and brands stand out in the digital noise with designs that not only look incredible but perform exceptionally.",
    },
    {
      title: "Our Process",
      content:
        "From concept to completion, we follow a proven process: Research your audience, analyze your niche, create multiple concepts, refine based on feedback, and deliver designs that convert viewers into subscribers.",
    },
    {
      title: "Our Promise",
      content:
        "Every project comes with our commitment to excellence. We guarantee unlimited revisions, fast turnaround times, and designs that are optimized for maximum click-through rates and engagement.",
    },
  ];

  const stats = [
    { number: "500+", label: "Projects Completed", icon: "fas fa-rocket" },
    { number: "98%", label: "Client Satisfaction", icon: "fas fa-heart" },
    { number: "24h", label: "Average Turnaround", icon: "fas fa-clock" },
    { number: "50M+", label: "Views Generated", icon: "fas fa-eye" },
  ];

  return (
    <section
      id='about'
      className='py-16 md:py-24 lg:py-32 px-6 lg:px-12 bg-white relative overflow-hidden'
      ref={ref}
    >
      {/* Background Elements */}
      <div className='absolute inset-0 z-0'>
        <motion.div
          className='absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-br from-[#FF4500]/10 to-[#FF6B35]/5 rounded-full blur-3xl'
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className='absolute bottom-1/4 right-1/4 w-48 h-48 bg-gradient-to-tl from-[#FFA500]/10 to-[#FF4500]/5 rounded-full blur-3xl'
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut",
            delay: -5,
          }}
        />
      </div>

      <div className='max-w-7xl mx-auto relative z-10'>
        {/* Header */}
        <motion.div
          className='text-center space-y-6 mb-16'
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.p
            className='text-sm font-semibold text-[#FF4500] uppercase tracking-wide'
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            ABOUT BIVISO
          </motion.p>

          <motion.h2
            className='text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight tracking-wide'
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            MEET THE{" "}
            <span className='bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent'>
              CREATIVE AGENCY
            </span>
          </motion.h2>
        </motion.div>

        {/* Main Content Grid */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center mb-16'>
          {/* Left Content */}
          <motion.div
            className='space-y-8'
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <div className='space-y-6'>
              <p className='text-lg text-gray-600 leading-relaxed'>
                We are <strong className='text-[#FF4500]'>Biviso</strong>, a
                creative design agency that specializes in transforming content
                creators' visions into scroll-stopping visuals that drive real
                results.
              </p>

              <p className='text-lg text-gray-600 leading-relaxed'>
                Our team combines creative expertise with data-driven insights
                to create thumbnails, social media designs, and visual branding
                that not only look incredible but perform exceptionally in
                today's competitive digital landscape.
              </p>
            </div>

            {/* Interactive Tabs */}
            <div className='space-y-4'>
              <div className='flex flex-wrap gap-2'>
                {tabs.map((tab, index) => (
                  <motion.button
                    key={index}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                      activeTab === index
                        ? "bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white shadow-lg"
                        : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                    }`}
                    onClick={() => setActiveTab(index)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {tab.title}
                  </motion.button>
                ))}
              </div>

              <motion.div
                key={activeTab}
                className='bg-gray-50 rounded-2xl p-6'
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <p className='text-gray-700 leading-relaxed'>
                  {tabs[activeTab].content}
                </p>
              </motion.div>
            </div>
          </motion.div>

          {/* Right Content - Stats */}
          <motion.div
            className='grid grid-cols-2 gap-6'
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                className='bg-white rounded-2xl p-6 shadow-lg border border-gray-100 text-center group hover:bg-gradient-to-br hover:from-[#FF4500]/5 hover:to-[#FF6B35]/5 transition-all duration-300'
                initial={{ opacity: 0, y: 30 }}
                animate={
                  isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }
                }
                transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                whileHover={{
                  y: -5,
                  boxShadow: "0 20px 40px rgba(255, 69, 0, 0.15)",
                }}
              >
                <motion.div
                  className='w-12 h-12 mx-auto mb-4 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-xl flex items-center justify-center'
                  whileHover={{ scale: 1.1, rotate: 5 }}
                >
                  <i className={`${stat.icon} text-white text-lg`} />
                </motion.div>
                <h3 className='text-2xl font-bold text-gray-900 mb-2 group-hover:text-[#FF4500] transition-colors duration-300'>
                  {stat.number}
                </h3>
                <p className='text-sm text-gray-600 font-medium'>
                  {stat.label}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
}
