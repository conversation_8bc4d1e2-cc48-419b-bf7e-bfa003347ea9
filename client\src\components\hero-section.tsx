import { useRef, useState, useEffect } from "react";
import { gsap } from "gsap";

export default function HeroSection() {
  const heroRef = useRef(null);
  const servicesRef = useRef(null);
  const subheadingRef = useRef(null);
  const ctaRef = useRef(null);
  const [currentServiceIndex, setCurrentServiceIndex] = useState(0);

  const services = [
    "YOUTUBE THUMBNAILS",
    "SOCIAL MEDIA DESIGN",
    "DESIGN CONSULTATION",
  ];

  // GSAP animations
  useEffect(() => {
    const ctx = gsap.context(() => {
      // Initial setup
      gsap.set(".service-text", { y: 100, opacity: 0 });
      gsap.set(subheadingRef.current, { y: 50, opacity: 0 });
      gsap.set(ctaRef.current, { y: 80, opacity: 0 });

      // Hero entrance animation
      const tl = gsap.timeline();

      tl.to(".service-text", {
        y: 0,
        opacity: 1,
        duration: 1.2,
        ease: "power3.out",
        stagger: 0.2,
      })
        .to(
          subheadingRef.current,
          {
            y: 0,
            opacity: 1,
            duration: 1,
            ease: "power2.out",
          },
          "-=0.5"
        )
        .to(
          ctaRef.current,
          {
            y: 0,
            opacity: 1,
            duration: 1,
            ease: "power2.out",
          },
          "-=0.3"
        );

      // Floating background elements
      gsap.to(".floating-element-1", {
        y: -30,
        x: 20,
        rotation: 360,
        duration: 8,
        repeat: -1,
        yoyo: true,
        ease: "power1.inOut",
      });

      gsap.to(".floating-element-2", {
        y: 25,
        x: -15,
        rotation: -180,
        duration: 6,
        repeat: -1,
        yoyo: true,
        ease: "power1.inOut",
      });
    }, heroRef);

    return () => ctx.revert();
  }, []);

  // Service rotation effect with GSAP
  useEffect(() => {
    const interval = setInterval(() => {
      const currentElement = document.querySelector(
        `[data-service="${currentServiceIndex}"]`
      );
      const nextIndex = (currentServiceIndex + 1) % services.length;
      const nextElement = document.querySelector(
        `[data-service="${nextIndex}"]`
      );

      if (currentElement && nextElement) {
        gsap.to(currentElement, {
          y: -50,
          opacity: 0,
          duration: 0.5,
          ease: "power2.in",
        });

        gsap.fromTo(
          nextElement,
          { y: 50, opacity: 0 },
          {
            y: 0,
            opacity: 1,
            duration: 0.5,
            ease: "power2.out",
            delay: 0.3,
          }
        );
      }

      setCurrentServiceIndex(nextIndex);
    }, 3000);

    return () => clearInterval(interval);
  }, [currentServiceIndex, services.length]);

  return (
    <section
      ref={heroRef}
      id='hero'
      className='min-h-screen flex items-center justify-center px-6 lg:px-12 py-32 relative overflow-hidden bg-gradient-to-br from-white via-gray-50 to-white'
    >
      {/* Floating Background Elements */}
      <div className='absolute inset-0 z-0'>
        <div className='floating-element-1 absolute top-1/4 left-1/4 w-20 h-20 border-4 border-[#FF4500]/30 rounded-2xl' />
        <div className='floating-element-2 absolute bottom-1/4 right-1/4 w-16 h-16 bg-gradient-to-br from-[#FF6B35] to-[#FFA500] rounded-full' />
        <div className='absolute top-1/3 right-1/3 w-12 h-12 border-2 border-[#FF6B35]/40 rounded-full' />
        <div className='absolute bottom-1/3 left-1/3 w-8 h-8 bg-[#FFA500]/60 rounded-full' />
      </div>

      <div className='max-w-7xl mx-auto text-center z-10 relative w-full'>
        <div className='space-y-16'>
          {/* Animated Services Showcase */}
          <div
            ref={servicesRef}
            className='relative h-24 md:h-32 lg:h-40 overflow-hidden'
          >
            {services.map((service, index) => (
              <div
                key={service}
                data-service={index}
                className={`service-text absolute inset-0 flex items-center justify-center ${
                  currentServiceIndex === index ? "opacity-100" : "opacity-0"
                }`}
              >
                <h1 className='text-4xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-gray-900 tracking-wider leading-none'>
                  {service}
                </h1>
              </div>
            ))}
          </div>

          {/* Subheading */}
          <div ref={subheadingRef} className='opacity-0'>
            <p className='text-xl md:text-2xl lg:text-3xl font-light text-gray-600 max-w-4xl mx-auto leading-relaxed tracking-[0.2em] uppercase'>
              Where Vision Meets Performance
            </p>
          </div>

          {/* CTA Buttons */}
          <div
            ref={ctaRef}
            className='flex flex-col sm:flex-row gap-6 items-center justify-center pt-8 opacity-0'
          >
            <a
              href='#contact'
              className='group relative px-8 py-4 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white font-semibold text-sm tracking-wide rounded-full shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300'
            >
              <span className='relative z-10 flex items-center uppercase'>
                <span className='mr-3'>START YOUR PROJECT</span>
                <i className='fas fa-rocket text-sm' />
              </span>
            </a>

            <a
              href='#portfolio'
              className='group relative px-8 py-4 bg-white text-[#FF4500] font-semibold text-sm tracking-wide rounded-full border-2 border-[#FF4500] shadow-xl hover:bg-[#FF4500] hover:text-white transition-all duration-300'
            >
              <span className='relative z-10 flex items-center uppercase'>
                <span className='mr-3'>VIEW PORTFOLIO</span>
                <i className='fas fa-arrow-right text-sm' />
              </span>
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
