import { useRef, useState, useEffect } from "react";

import { motion } from "framer-motion";

export default function HeroSection() {
  const heroRef = useRef(null);
  const servicesRef = useRef(null);
  const subheadingRef = useRef(null);
  const ctaRef = useRef(null);
  const [currentServiceIndex, setCurrentServiceIndex] = useState(0);

  const services = [
    "YOUTUBE THUMBNAILS",
    "SOCIAL MEDIA DESIGN",
    "DESIGN CONSULTATION",
  ];

  // Service rotation effect
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentServiceIndex((prev) => (prev + 1) % services.length);
    }, 3000);
    return () => clearInterval(interval);
  }, [services.length]);

  // Service rotation effect with GSAP
  useEffect(() => {
    const interval = setInterval(() => {
      const currentElement = document.querySelector(
        `[data-service="${currentServiceIndex}"]`
      );
      const nextIndex = (currentServiceIndex + 1) % services.length;
      const nextElement = document.querySelector(
        `[data-service="${nextIndex}"]`
      );

      if (currentElement && nextElement) {
        gsap.to(currentElement, {
          y: -50,
          opacity: 0,
          duration: 0.5,
          ease: "power2.in",
        });

        gsap.fromTo(
          nextElement,
          { y: 50, opacity: 0 },
          {
            y: 0,
            opacity: 1,
            duration: 0.5,
            ease: "power2.out",
            delay: 0.3,
          }
        );
      }

      setCurrentServiceIndex(nextIndex);
    }, 3000);

    return () => clearInterval(interval);
  }, [currentServiceIndex, services.length]);

  return (
    <motion.section
      ref={heroRef}
      id='hero'
      className='min-h-screen flex items-center justify-center px-6 lg:px-12 py-32 relative overflow-hidden bg-gradient-to-br from-white via-gray-50 to-white'
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 1 }}
    >
      {/* Floating Background Elements */}
      <div className='absolute inset-0 z-0'>
        <motion.div
          className='absolute top-1/4 left-1/4 w-20 h-20 border-4 border-[#FF4500]/30 rounded-2xl'
          animate={{
            rotate: [0, 360],
            y: [0, -30, 0],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className='absolute bottom-1/4 right-1/4 w-16 h-16 bg-gradient-to-br from-[#FF6B35] to-[#FFA500] rounded-full'
          animate={{
            y: [0, -40, 0],
            x: [0, 20, 0],
            scale: [1, 1.3, 1],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            delay: -2,
          }}
        />
      </div>

      <div className='max-w-7xl mx-auto text-center z-10 relative w-full'>
        <div className='space-y-16'>
          {/* Animated Services Showcase */}
          <div className='relative h-24 md:h-32 lg:h-40 overflow-hidden'>
            {services.map((service, index) => (
              <motion.div
                key={service}
                className='absolute inset-0 flex items-center justify-center'
                initial={{
                  opacity: 0,
                  y: 60,
                  rotateX: 90,
                }}
                animate={{
                  opacity: currentServiceIndex === index ? 1 : 0,
                  y:
                    currentServiceIndex === index
                      ? 0
                      : currentServiceIndex > index
                      ? -60
                      : 60,
                  rotateX: currentServiceIndex === index ? 0 : 90,
                }}
                transition={{
                  duration: 1,
                  ease: [0.25, 0.46, 0.45, 0.94],
                  type: "spring",
                  stiffness: 200,
                  damping: 25,
                }}
              >
                <h1 className='text-4xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-gray-900 tracking-wider leading-none'>
                  {service}
                </h1>
              </motion.div>
            ))}
          </div>

          {/* Subheading */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
          >
            <p className='text-xl md:text-2xl lg:text-3xl font-light text-gray-600 max-w-4xl mx-auto leading-relaxed tracking-[0.2em] uppercase'>
              Where Vision Meets Performance
            </p>
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            className='flex flex-col sm:flex-row gap-6 items-center justify-center pt-8'
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.6 }}
          >
            <motion.a
              href='#contact'
              className='group relative px-8 py-4 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white font-semibold text-sm tracking-wide rounded-full shadow-xl overflow-hidden'
              whileHover={{
                scale: 1.05,
                boxShadow: "0 20px 40px rgba(255, 69, 0, 0.3)",
              }}
              whileTap={{ scale: 0.95 }}
            >
              <span className='relative z-10 flex items-center uppercase'>
                <span className='mr-3'>START YOUR PROJECT</span>
                <i className='fas fa-rocket text-sm' />
              </span>
            </motion.a>

            <motion.a
              href='#portfolio'
              className='group relative px-8 py-4 bg-white text-[#FF4500] font-semibold text-sm tracking-wide rounded-full border-2 border-[#FF4500] shadow-xl hover:bg-[#FF4500] hover:text-white transition-all duration-300'
              whileHover={{
                scale: 1.05,
                boxShadow: "0 20px 40px rgba(255, 69, 0, 0.2)",
              }}
              whileTap={{ scale: 0.95 }}
            >
              <span className='relative z-10 flex items-center uppercase'>
                <span className='mr-3'>VIEW PORTFOLIO</span>
                <i className='fas fa-arrow-right text-sm' />
              </span>
            </motion.a>
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
}
