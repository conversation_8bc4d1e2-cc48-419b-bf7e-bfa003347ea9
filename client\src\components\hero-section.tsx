import { motion, useScroll, useTransform } from "framer-motion";
import { useRef, useState, useEffect } from "react";

export default function HeroSection() {
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start start", "end start"],
  });

  const y = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const opacity = useTransform(scrollYProgress, [0, 1], [1, 0]);
  const scale = useTransform(scrollYProgress, [0, 1], [1, 0.8]);

  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [currentServiceIndex, setCurrentServiceIndex] = useState(0);

  const services = [
    "YOUTUBE THUMBNAILS",
    "SOCIAL MEDIA DESIGN",
    "DESIGN CONSULTATION",
  ];

  useEffect(() => {
    const updateMousePosition = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };
    window.addEventListener("mousemove", updateMousePosition);
    return () => window.removeEventListener("mousemove", updateMousePosition);
  }, []);

  // Service rotation effect
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentServiceIndex((prev) => (prev + 1) % services.length);
    }, 3000);
    return () => clearInterval(interval);
  }, [services.length]);

  const backgroundVariants = {
    animate: {
      background: [
        "linear-gradient(135deg, #fff 0%, #fff5eb 50%, #fff 100%)",
        "linear-gradient(135deg, #fff5eb 0%, #fff 50%, #ffede0 100%)",
        "linear-gradient(135deg, #ffede0 0%, #fff5eb 50%, #fff 100%)",
      ],
      transition: {
        duration: 8,
        repeat: Infinity,
        ease: "easeInOut",
      },
    },
  };

  return (
    <motion.section
      ref={ref}
      id='hero'
      className='min-h-screen flex items-center justify-center px-6 lg:px-12 py-32 relative overflow-hidden'
      variants={backgroundVariants}
      animate='animate'
    >
      {/* Dynamic Background with Mouse Tracking - Hidden on mobile */}
      <div className='absolute inset-0 z-0 hidden md:block'>
        {/* Magnetic floating elements */}
        <motion.div
          className='absolute w-80 h-80 bg-gradient-to-br from-[#FF4500]/20 via-[#FF6B35]/15 to-[#FFA500]/10 rounded-full blur-3xl'
          style={{
            left: mousePosition.x * 0.02 + 100,
            top: mousePosition.y * 0.02 + 100,
          }}
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />

        <motion.div
          className='absolute w-60 h-60 bg-gradient-to-tl from-[#FFA500]/15 via-[#FF4500]/20 to-[#FF6B35]/10 rounded-full blur-3xl'
          style={{
            right: mousePosition.x * -0.01 + 150,
            bottom: mousePosition.y * -0.01 + 150,
          }}
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut",
            delay: -5,
          }}
        />

        {/* Geometric floating elements */}
        <motion.div
          className='absolute top-1/4 left-1/4 w-20 h-20 border-4 border-[#FF4500]/30 rounded-2xl'
          animate={{
            rotate: [0, 360],
            y: [0, -30, 0],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />

        <motion.div
          className='absolute bottom-1/4 right-1/4 w-16 h-16 bg-gradient-to-br from-[#FF6B35] to-[#FFA500] rounded-full'
          animate={{
            y: [0, -40, 0],
            x: [0, 20, 0],
            scale: [1, 1.3, 1],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            delay: -2,
          }}
        />

        {/* Additional Creative Elements */}
        <motion.div
          className='absolute top-1/3 right-1/3 w-12 h-12 border-2 border-[#FF6B35]/40 rounded-full'
          animate={{
            rotate: [0, -360],
            scale: [1, 1.5, 1],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: -3,
          }}
        />

        <motion.div
          className='absolute bottom-1/3 left-1/3 w-8 h-8 bg-[#FFA500]/60 rounded-full'
          animate={{
            y: [0, -25, 0],
            x: [0, -15, 0],
            opacity: [0.6, 1, 0.6],
          }}
          transition={{
            duration: 7,
            repeat: Infinity,
            ease: "easeInOut",
            delay: -1,
          }}
        />

        <motion.div
          className='absolute top-1/2 left-1/6 w-6 h-6 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-full'
          animate={{
            scale: [1, 1.8, 1],
            opacity: [0.4, 0.8, 0.4],
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: -4,
          }}
        />

        <motion.div
          className='absolute top-1/2 right-1/6 w-14 h-14 border-3 border-[#FFA500]/30 rounded-lg'
          animate={{
            rotate: [0, 180, 360],
            y: [0, -20, 0],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: -6,
          }}
        />
      </div>

      <motion.div
        className='max-w-7xl mx-auto text-center z-10 relative w-full'
        style={{ y, opacity, scale }}
      >
        <div className='space-y-12'>
          {/* Enhanced Main Headline */}
          <motion.div
            className='space-y-8'
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
          >
            {/* Main Title */}
            <motion.div
              className='relative'
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.2 }}
            >
              <h1 className='text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight text-center text-gray-900 tracking-wider'>
                <motion.span
                  className='block mb-4'
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                >
                  THUMBNAILS
                </motion.span>
                <motion.span
                  className='block bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent'
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                >
                  THAT CLICK
                </motion.span>
              </h1>
            </motion.div>

            {/* Animated Services Showcase */}
            <motion.div
              className='relative h-16 md:h-20 lg:h-24 overflow-hidden'
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.8 }}
            >
              {services.map((service, index) => (
                <motion.div
                  key={service}
                  className='absolute inset-0 flex items-center justify-center'
                  initial={{
                    opacity: 0,
                    y: 60,
                    rotateX: 90,
                  }}
                  animate={{
                    opacity: currentServiceIndex === index ? 1 : 0,
                    y:
                      currentServiceIndex === index
                        ? 0
                        : currentServiceIndex > index
                        ? -60
                        : 60,
                    rotateX: currentServiceIndex === index ? 0 : 90,
                  }}
                  transition={{
                    duration: 1,
                    ease: [0.25, 0.46, 0.45, 0.94],
                    type: "spring",
                    stiffness: 200,
                    damping: 25,
                  }}
                >
                  <span className='text-xl md:text-2xl lg:text-3xl font-semibold text-gray-700 tracking-widest'>
                    {service}
                  </span>
                </motion.div>
              ))}
            </motion.div>

            <motion.p
              className='text-lg md:text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed text-center tracking-wide'
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.2 }}
            >
              WHERE VISION MEETS PERFORMANCE
            </motion.p>
          </motion.div>

          {/* Enhanced CTA Buttons */}
          <motion.div
            className='flex flex-col sm:flex-row gap-6 items-center justify-center pt-12'
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.6 }}
          >
            <motion.a
              href='#contact'
              className='group relative px-8 py-4 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white font-semibold text-sm tracking-wide rounded-full shadow-xl overflow-hidden'
              whileHover={{
                scale: 1.05,
                boxShadow: "0 20px 40px rgba(255, 69, 0, 0.3)",
              }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 40, rotateX: 45 }}
              animate={{ opacity: 1, y: 0, rotateX: 0 }}
              transition={{
                duration: 1,
                delay: 1.8,
                type: "spring",
                stiffness: 200,
                damping: 20,
              }}
            >
              <motion.div
                className='absolute inset-0 bg-gradient-to-r from-[#FF6B35] to-[#FF4500] opacity-0 group-hover:opacity-100 transition-opacity duration-300'
                whileHover={{ scale: 1.1 }}
              />
              <span className='relative z-10 flex items-center uppercase'>
                <span className='mr-3'>START YOUR PROJECT</span>
                <motion.i
                  className='fas fa-rocket text-sm'
                  whileHover={{ x: 5 }}
                  transition={{ duration: 0.2 }}
                />
              </span>
            </motion.a>

            <motion.a
              href='#portfolio'
              className='group relative px-8 py-4 bg-white text-[#FF4500] font-semibold text-sm tracking-wide rounded-full border-2 border-[#FF4500] shadow-xl hover:bg-[#FF4500] hover:text-white transition-all duration-300'
              whileHover={{
                scale: 1.05,
                boxShadow: "0 20px 40px rgba(255, 69, 0, 0.2)",
              }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 40, rotateX: 45 }}
              animate={{ opacity: 1, y: 0, rotateX: 0 }}
              transition={{
                duration: 1,
                delay: 2,
                type: "spring",
                stiffness: 200,
                damping: 20,
              }}
            >
              <span className='relative z-10 flex items-center uppercase'>
                <span className='mr-3'>VIEW PORTFOLIO</span>
                <motion.i
                  className='fas fa-arrow-right text-sm'
                  whileHover={{ x: 5 }}
                  transition={{ duration: 0.2 }}
                />
              </span>
            </motion.a>
          </motion.div>
        </div>
      </motion.div>
    </motion.section>
  );
}
